import { useEffect, useState } from "react"
import { Link, useNavigate } from "react-router-dom";
import DiscountAlert from "../components/DiscountAlert";


const Ticket = () => {
  let navigate = useNavigate();

  // Function to get token from both storage locations
  function getToken() {
    const sessionToken = sessionStorage.getItem('token');
    const localToken = localStorage.getItem('token');
    return sessionToken || localToken;
  }

  const [services, setServices] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [formData, setFormData] = useState({
    subject: '',
    related_service: '',
    department: '',
    priority: 'Low',
    message: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    // Fetch user services for dropdown
    const token = getToken();
    if (!token) {
      console.error("No authentication token found");
      setServices([]);
      return;
    }

    fetch("/api.php?f=user_services", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
    .then(response => response.json())
    .then(data => {
      // Ensure data is an array before setting it
      if (Array.isArray(data)) {
        setServices(data);
      } else {
        console.error("Services data is not an array:", data);
        setServices([]); // Set to empty array to avoid mapping errors
      }
    })
    .catch(error => {
      console.error("Error fetching services:", error);
      setServices([]); // Set to empty array on error
    });
  }, []);

  useEffect(() => {
    // Fetch departments for dropdown
    const token = getToken();
    if (!token) {
      console.error("No authentication token found");
      setDepartments([]);
      return;
    }

    fetch("/api.php?f=get_departments", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
    .then(response => response.json())
    .then(data => {
      // Ensure data is an array before setting it
      if (Array.isArray(data)) {
        setDepartments(data);
        // Set default department to the first one if available
        if (data.length > 0 && !formData.department) {
          setFormData(prev => ({
            ...prev,
            department: data[0].department_name
          }));
        }
      } else {
        console.error("Departments data is not an array:", data);
        setDepartments([]); // Set to empty array to avoid mapping errors
      }
    })
    .catch(error => {
      console.error("Error fetching departments:", error);
      setDepartments([]); // Set to empty array on error
    });
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    // Validate form
    if (!formData.subject.trim()) {
      setError('Subject is required');
      setLoading(false);
      return;
    }
    
    if (!formData.message.trim()) {
      setError('Message is required');
      setLoading(false);
      return;
    }

    // Get token from storage
    const token = getToken();
    if (!token) {
      setError('Authentication error. Please try logging in again.');
      setLoading(false);
      return;
    }

    // Log the data being sent (for debugging)
    console.log('Submitting ticket with data:', formData);
    console.log('Using token:', token);

    // Prepare data with token included in the body
    const requestData = {
      ...formData,
      token: token // Include token in request body
    };

    // Submit ticket
    fetch("/api.php?f=create_ticket", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })
    .then(response => {
      console.log('Response status:', response.status);
      return response.json().catch(e => {
        // Handle non-JSON responses
        console.error('Error parsing JSON:', e);
        throw new Error('Invalid response format from server');
      });
    })
    .then(data => {
      setLoading(false);
      console.log('Response data:', data);
      
      if (data.success) {
        setSuccess(true);
        // Redirect to ticket view after 1.5 seconds
        setTimeout(() => {
          navigate(`/support/${data.ticket_id}`);
        }, 1500);
      } else {
        // Show detailed error information
        if (data.error === 3) {
          setError('Please fill in all required fields.');
        } else if (data.error === 4) {
          setError('Authentication error. Please try logging in again.');
        } else if (data.error === 6) {
          setError('Database error: ' + (data.message || 'Unknown error'));
        } else {
          setError(data.message || 'Failed to create ticket. Please try again.');
        }
      }
    })
    .catch(error => {
      setLoading(false);
      console.error("Error creating ticket:", error);
      setError('Network error: ' + error.message);
    });
  };

  return (
    <>
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/support">Support</Link></li>
            <li className="breadcrumb-item1">New Case</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card noheight">
            <div className="card-header">
              <div className="card-title">New Case</div>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="card-body">
                {error && (
                  <div className="alert alert-danger" role="alert">
                    <i className="fa fa-exclamation-triangle me-2"></i> {error}
                  </div>
                )}
                {success && (
                  <div className="alert alert-success" role="alert">
                    <i className="fa fa-check-circle me-2"></i> Ticket created successfully! Redirecting...
                  </div>
                )}
                <div className="row">
                  <div className="col-sm-6 col-md-6">
                    <div className="form-group">
                      Subject
                      <input 
                        type="text" 
                        className="form-control" 
                        placeholder="Subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-sm-6 col-md-6">
                    <div className="form-group">
                      Related Service
                      <select 
                        className="form-control nice-select select2"
                        name="related_service"
                        value={formData.related_service}
                        onChange={handleInputChange}
                      >
                        <option value="">--New Service--</option>
                        {services && services.length > 0 ? (
                          services.map(service => (
                            <option key={service.id} value={service.id}>
                              {service.label}
                            </option>
                          ))
                        ) : (
                          <option disabled value="">No services available</option>
                        )}
                      </select>
                    </div>
                  </div>
                  <div className="col-sm-6 col-md-6">
                    <div className="form-group">
                      Department
                      <select
                        className="form-control nice-select select2"
                        name="department"
                        value={formData.department}
                        onChange={handleInputChange}
                      >
                        {departments && departments.length > 0 ? (
                          departments.map(department => (
                            <option key={department.id} value={department.department_name}>
                              {department.department_name}
                            </option>
                          ))
                        ) : (
                          <option value="">Loading departments...</option>
                        )}
                      </select>
                    </div>
                  </div>
                  <div className="col-sm-6 col-md-6">
                    <div className="form-group">
                      Priority
                      <select 
                        className="form-control nice-select select2"
                        name="priority"
                        value={formData.priority}
                        onChange={handleInputChange}
                      >
                        <option value="Low">Low</option>
                        <option value="Medium">Medium</option>
                        <option value="High">High</option>
                        <option value="Urgent">Urgent</option>
                      </select>
                    </div>
                  </div>

                  <div className="col-sm-12 col-md-12">
                    <div className="form-group">
                      Message
                      <textarea 
                        className="form-control" 
                        rows="5"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                      ></textarea>
                    </div>
                  </div>
                </div>
                <b>*Our services are unmanaged. You should only contact NOC for matters that are not under your control (network issues, availability or hardware failures)<br/>**Sales and Billing departments are only available Monday by Friday between 09:00 and 18:00 AM GMT+3<br/><font color="red">Please open a single case for one matter. Opening multiple cases for the same matter will slower the answering time.</font></b>
              </div>

              <div className="card-footer text-right">
                <button 
                  type="submit" 
                  className="btn btn-success"
                  disabled={loading}
                >
                  {loading ? 'Creating...' : 'Open Case'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default Ticket;